steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-f', 'Dockerfile',
      '-t', 'asia-south1-docker.pkg.dev/reviewdale-datafabric/rd-artfct-data-harvester/spider:$_FOLDER_PATH',
      '--build-arg', 'FOLDER_PATH=$_FOLDER_PATH',
      '.'
    ]
    dir: 'C001/spiders'

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'asia-south1-docker.pkg.dev/reviewdale-datafabric/rd-artfct-data-harvester/spider:$_FOLDER_PATH']

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        gcloud run jobs describe $_FOLDER_PATH --region=asia-south1 && \
        gcloud run jobs delete $_FOLDER_PATH --region=asia-south1 --quiet || echo "No existing job to delete"

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args: [
      'run', 'jobs', 'create', '$_FOLDER_PATH',
      '--image', 'asia-south1-docker.pkg.dev/reviewdale-datafabric/rd-artfct-data-harvester/spider:$_FOLDER_PATH',
      '--region', 'asia-south1',
      '--task-timeout', '86400s',
      '--memory', '3Gi'
    ]

  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args: [
      'run', 'jobs', 'execute', '$_FOLDER_PATH',
      '--region', 'asia-south1'
    ]

substitutions:
  _FOLDER_PATH: 'flipkart'

options:
  default_logs_bucket_behavior: REGIONAL_USER_OWNED_BUCKET
