import { getPage, closeBrowser } from './config/puppeterInstance.js';
import { PRODUCTS } from './settings.js'
import { fetchProductInfo } from './utils/cheerio.js';
import { randomScroll, randomMouseMoves, randInt, matchProductName, uploadProductInfo } from './utils/util.js';

async function searchProduct(productName) {
    const page = await getPage();
    await page.goto('https://www.smartprix.com/', { waitUntil: 'domcontentloaded' });
    await page.waitForSelector('input[name="q"]', { timeout: 70000 });

    await randomMouseMoves(page, randInt(2, 5));
    await randomScroll(page);

    const searchBox = await page.$('input[name="q"]');
    const box = await searchBox.boundingBox();
    if (box) {
        await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
        await page.mouse.click(box.x + box.width / 2, box.y + box.height / 2);
    }

    await new Promise(res => setTimeout(res, randInt(80, 300)));

    await page.type('input[name="q"]', productName, { delay: randInt(50, 110) });
    await page.keyboard.press('Enter');
    await page.waitForSelector('.pg-prf-head', { timeout: 70000 });

    await randomScroll(page);
    await randomMouseMoves(page, randInt(1, 3));

    const products = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('a.name.clamp-2')).map(a => ({
            name: a.innerText.trim(),
            url: a.href.startsWith('http') ? a.href : `https://www.smartprix.com${a.getAttribute('href')}`,
        }));
    });

    await page.close();
    const matchedProducts = await matchProductName(productName, products);
    console.log(`Success: Matched ${productName} : ${matchedProducts}`);
    
    return matchedProducts.length >0 ? matchedProducts[0] : null;
}

async function fetchProductDetail(product) {
    const page = await getPage();
    await page.goto(product.url, { waitUntil: 'domcontentloaded' });

    await randomScroll(page);
    await randomMouseMoves(page, randInt(1, 2));

    const productDetailHTML = await page.content();
    await page.close();
    const productInfo = await fetchProductInfo(productDetailHTML);
    return productInfo;
}


async function main() {
    try {
        for (let product = 0; product < PRODUCTS.length; product++) {
            const matchedProduct = await searchProduct(PRODUCTS[product].product_name);
            console.log("Success: Matched: ", matchedProduct);
            if(!matchedProduct) {
                continue;
            }
            const productDetailInfo = await fetchProductDetail(matchedProduct);
            await uploadProductInfo(productDetailInfo, PRODUCTS[product].id)
            console.log("Wait 2 secs...");
            await new Promise(res => setTimeout(res, 2000));
        }
        await closeBrowser();
        process.exit(0);
    } catch (error) {
        console.log(error);
        await closeBrowser();
        process.exit(1);
    }
}

main();
