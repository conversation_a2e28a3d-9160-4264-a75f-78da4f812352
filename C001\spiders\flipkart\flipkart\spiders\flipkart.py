import scrapy  # type: ignore
import re
import json
import os
from dotenv import load_dotenv # type: ignore
load_dotenv()
from flipkart.items import FlipkartReviewItem
from flipkart.utils import parse_custom_date, match_product_name

class FlipkartSpider(scrapy.Spider):
    name = "flipkart"
    allowed_domains = ["flipkart.com"]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        current_dir = os.path.dirname(os.path.abspath(__file__))
        json_path = os.path.join(current_dir, 'products.json')
        self.logger.info(f"data: {os.getenv('ENV', 'dev')}, {os.getenv('GCS_BUCKET_NAME', '')} ")
        
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            self.products = data.get("products", [])

    def start_requests(self):
        for product in self.products:
            product_name = product.get("product_name")
            product_id = product.get("id")
            if not product_name:
                continue

            query = product_name
            search_url = f"https://www.flipkart.com/search?q={query}"
            yield scrapy.Request(
                search_url,
                callback=self.parse_search,
                meta={
                    'product_name': product_name,
                    'product_id': product_id
                }
            )

    def parse_search(self, response):
        product_object = []
        for product in response.css('div.tUxRFH'):
            link = product.css('a.CGtC98::attr(href)').get()
            name = product.css('div.KzDlHZ::text').get()

            product_object.append({
                'name': name,
                'link': link
            })
            
        product_name = response.meta['product_name']
        product_id = response.meta['product_id']
        matched_products = match_product_name(product_object, product_name)
        if len(matched_products) > 0:
            product_detail_url = 'https://www.flipkart.com' + matched_products[0]['link']
            yield scrapy.Request(
                product_detail_url,
                callback=self.parse_product_detail_page,
                meta={'product_name': product_name, 'product_id': product_id, 'product_url': product_detail_url}
            )
        else:
            self.logger.warning(f"No product links found for {product_name}")

    def parse_product_detail_page(self, response):
        product_name = response.meta['product_name']
        product_id = response.meta['product_id']
        product_url = response.meta['product_url']
        review_links = response.css('div.col.pPAw9M a::attr(href)').getall()

        if review_links:
            full_review_url = "https://www.flipkart.com" + review_links[-1]
            yield scrapy.Request(
                full_review_url,
                callback=self.parse_review_detail_page,
                meta={'product_name': product_name, 'product_id': product_id, 'product_url': product_url}
            )
        else:
            self.logger.warning(f"[{product_name}] Review link not found!")

    def parse_review_detail_page(self, response):
        product_name = response.meta['product_name']
        product_id = response.meta['product_id']
        product_url = response.meta['product_url']

        review_details_next_url = response.xpath("//a[contains(@class, 'cn++Ap')]/@href").get()
        if not review_details_next_url:
            self.logger.warning(f"No next review URL found for {product_name}")
            return

        clean_url = re.sub(r'([&?])(page|sortOrder)=[^&]*', '', review_details_next_url or '')
        clean_url = re.sub(r'\?&', '?', clean_url)
        clean_url = re.sub(r'[&?]+$', '', clean_url)
        clean_url = re.sub(r'\?$', '', clean_url)
        if '?' in review_details_next_url and '?' not in clean_url:
            clean_url = clean_url.replace('&', '?', 1)

        yield from self.extract_reviews(response, product_name, product_id, product_url)
        
        all_review_urls = []
        most_helpful_review_url = f"https://www.flipkart.com{clean_url}&sortOrder=MOST_HELPFUL"
        most_recent_review_url = f"https://www.flipkart.com{clean_url}&sortOrder=MOST_RECENT"
        positive_first_review_url = f"https://www.flipkart.com{clean_url}&sortOrder=POSITIVE_FIRST"
        negative_first_review_url = f"https://www.flipkart.com{clean_url}&sortOrder=NEGATIVE_FIRST"

        for page in range(1, 10):
            all_review_urls.append(most_helpful_review_url + f"&page={page}")
            all_review_urls.append(most_recent_review_url + f"&page={page}")
            all_review_urls.append(positive_first_review_url + f"&page={page}")
            all_review_urls.append(negative_first_review_url + f"&page={page}")

        for page in all_review_urls:
            yield scrapy.Request(
                url=page,
                callback=self.extract_reviews,
                cb_kwargs={'product_name': product_name, 'product_id': product_id, 'product_url': product_url}
            )

    def extract_reviews(self, response, product_name, product_id, product_url):
        for review in response.css('div.col.EPCmJX.Ma1fCG'):
            item = FlipkartReviewItem()
            full_id = review.css('p.MztJPv::attr(id)').get()
            review_id = ''
            if full_id and full_id.startswith("review-"):
                review_id = full_id.replace("review-", "")
                
            dates = review.css('p._2NsDsF::text').getall()
            date = dates[1].strip() if len(dates) > 1 else ''
            item['date'] = parse_custom_date(date)
            item['downvotes'] = review.css('div._6kK6mk.aQymJL span.tl9VpF::text').get(default='0').strip()
            item['product_id'] = product_id
            item['product_name'] = product_name
            item['review_id'] = review_id
            review_value = review.xpath('.//div[@class="XQDdHH Ga3i8K"]/text()').get()
            item['review_value'] = int(review_value) if review_value and review_value.isdigit() else None
            item['review_title'] = review.css('p.z9E0IG::text').get(default='').strip()
            item['review_text'] = review.css('div.ZmyHeo div::text').get(default='').strip()
            item['upvotes'] = review.css('div._6kK6mk span.tl9VpF::text').get(default='0').strip()
            item['username'] = review.css('p._2NsDsF.AwS1CA::text').get(default='').strip()
            item['product_url'] = product_url

            yield item

    def slugify(self, text):
        return re.sub(r'\W+', '_', text.lower()).strip('_')
