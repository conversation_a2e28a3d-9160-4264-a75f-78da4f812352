# TODO: Make docker config in same file for scrapy and custom framework
# FROM python:3.10-slim
# WORKDIR /app
# ARG FOLDER_PATH
# ENV FOLDER_PATH=$FOLDER_PATH
# COPY $FOLDER_PATH /app/
# RUN pip install --no-cache-dir -r /app/requirements.txt
# ENV PYTHONPATH=/app
# CMD scrapy crawl $FOLDER_PATH

FROM node:20-slim
WORKDIR /app
RUN apt-get update && \
    apt-get install -y wget gnupg ca-certificates && \
    wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - && \
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list && \
    apt-get update && \
    apt-get install -y chromium && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
ARG FOLDER_PATH
ENV FOLDER_PATH=$FOLDER_PATH
COPY $FOLDER_PATH/package*.json ./
RUN npm ci --omit=dev
COPY $FOLDER_PATH/ .
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
CMD [ "node", "main.js" ]
